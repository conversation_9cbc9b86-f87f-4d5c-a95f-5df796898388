import mongoose, { Document, Schema, Types } from 'mongoose';

export interface INote extends Document {
  userId: Types.ObjectId;
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

const noteSchema = new Schema<INote>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
      minlength: [1, 'Title cannot be empty'],
      maxlength: [200, 'Title cannot exceed 200 characters']
    },
    content: {
      type: String,
      required: [true, 'Content is required'],
      trim: true,
      minlength: [1, 'Content cannot be empty'],
      maxlength: [10000, 'Content cannot exceed 10,000 characters']
    }
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Indexes for better query performance
noteSchema.index({ userId: 1, createdAt: -1 }); // For fetching user's notes sorted by creation date
noteSchema.index({ userId: 1, title: 'text', content: 'text' }); // For text search within user's notes

// Virtual for note preview (first 100 characters of content)
noteSchema.virtual('preview').get(function() {
  return this.content.length > 100 
    ? this.content.substring(0, 100) + '...' 
    : this.content;
});

// Static method to get notes for a user with pagination
noteSchema.statics.getUserNotes = async function(
  userId: string, 
  page: number = 1, 
  limit: number = 10,
  search?: string
) {
  const skip = (page - 1) * limit;
  
  let query: any = { userId };
  
  // Add text search if search term is provided
  if (search && search.trim()) {
    query.$text = { $search: search.trim() };
  }
  
  const notes = await this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();
    
  const total = await this.countDocuments(query);
  
  return {
    notes,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  };
};

// Static method to get note statistics for a user
noteSchema.statics.getUserStats = async function(userId: string) {
  const stats = await this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalNotes: { $sum: 1 },
        totalWords: { 
          $sum: { 
            $size: { 
              $split: [{ $trim: { input: "$content" } }, " "] 
            } 
          } 
        },
        avgWordsPerNote: { 
          $avg: { 
            $size: { 
              $split: [{ $trim: { input: "$content" } }, " "] 
            } 
          } 
        },
        lastUpdated: { $max: "$updatedAt" }
      }
    }
  ]);
  
  return stats[0] || {
    totalNotes: 0,
    totalWords: 0,
    avgWordsPerNote: 0,
    lastUpdated: null
  };
};

// Pre-save middleware to update the updatedAt field
noteSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

const Note = mongoose.model<INote>('Note', noteSchema);

export default Note;
