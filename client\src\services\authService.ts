import { apiUtils } from '../config/api';
import { 
  User, 
  RequestOtpData, 
  LoginCredentials, 
  AuthResponse, 
  ApiResponse 
} from '../types';

class AuthService {
  // Request OTP for email verification
  async requestOtp(data: RequestOtpData): Promise<ApiResponse> {
    try {
      return await apiUtils.post('/auth/request-otp', data);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Verify OTP and login/signup
  async verifyOtp(data: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiUtils.post<AuthResponse>('/auth/verify-otp', data);
      
      // Store user data in localStorage
      if (response.success && response.data?.user) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return response;
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Get current user
  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    try {
      return await apiUtils.get<{ user: User }>('/auth/me');
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Refresh access token
  async refreshToken(): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiUtils.post<AuthResponse>('/auth/refresh');
      
      // Update stored user data
      if (response.success && response.data?.user) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return response;
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Logout user
  async logout(): Promise<ApiResponse> {
    try {
      const response = await apiUtils.post('/auth/logout');
      
      // Clear stored user data
      localStorage.removeItem('user');
      
      return response;
    } catch (error) {
      // Even if logout fails on server, clear local data
      localStorage.removeItem('user');
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Google OAuth login
  initiateGoogleLogin(): void {
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    window.location.href = `${apiUrl}/auth/google`;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    const user = this.getStoredUser();
    return !!user;
  }

  // Get stored user from localStorage
  getStoredUser(): User | null {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      localStorage.removeItem('user');
      return null;
    }
  }

  // Update stored user data
  updateStoredUser(user: User): void {
    localStorage.setItem('user', JSON.stringify(user));
  }

  // Clear stored user data
  clearStoredUser(): void {
    localStorage.removeItem('user');
  }

  // Validate email format
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Validate OTP format
  validateOtp(otp: string): boolean {
    const otpRegex = /^\d{6}$/;
    return otpRegex.test(otp);
  }

  // Validate name format
  validateName(name: string): boolean {
    return name.trim().length >= 2 && name.trim().length <= 50;
  }

  // Get validation error message
  getValidationError(field: string, value: string): string | null {
    switch (field) {
      case 'email':
        if (!value.trim()) return 'Email is required';
        if (!this.validateEmail(value)) return 'Please enter a valid email address';
        return null;
      
      case 'name':
        if (!value.trim()) return 'Name is required';
        if (!this.validateName(value)) return 'Name must be between 2 and 50 characters';
        return null;
      
      case 'otp':
        if (!value.trim()) return 'OTP is required';
        if (!this.validateOtp(value)) return 'OTP must be a 6-digit number';
        return null;
      
      default:
        return null;
    }
  }

  // Format error message for display
  formatErrorMessage(error: string): string {
    // Common error message mappings
    const errorMappings: { [key: string]: string } = {
      'OTP not found or expired': 'Your verification code has expired. Please request a new one.',
      'Invalid OTP': 'The verification code you entered is incorrect. Please try again.',
      'Too many failed attempts': 'Too many incorrect attempts. Please request a new verification code.',
      'User not found': 'No account found with this email address.',
      'Invalid or expired access token': 'Your session has expired. Please sign in again.',
      'Network Error': 'Unable to connect to the server. Please check your internet connection.',
    };

    return errorMappings[error] || error;
  }

  // Check if error requires new OTP
  requiresNewOtp(error: string): boolean {
    const otpErrors = [
      'OTP not found or expired',
      'OTP has expired',
      'Too many failed attempts'
    ];
    
    return otpErrors.some(otpError => error.includes(otpError));
  }
}

export default new AuthService();
