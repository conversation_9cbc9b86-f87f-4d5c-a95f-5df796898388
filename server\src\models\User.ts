import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  email: string;
  name: string;
  avatarUrl?: string;
  provider: 'email' | 'google';
  googleId?: string;
  createdAt: Date;
  updatedAt: Date;
}

const userSchema = new Schema<IUser>(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        'Please provide a valid email address'
      ]
    },
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      minlength: [2, 'Name must be at least 2 characters long'],
      maxlength: [50, 'Name cannot exceed 50 characters']
    },
    avatarUrl: {
      type: String,
      default: null,
      validate: {
        validator: function(v: string) {
          if (!v) return true; // Allow null/undefined
          return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
        },
        message: 'Avatar URL must be a valid image URL'
      }
    },
    provider: {
      type: String,
      enum: ['email', 'google'],
      required: [true, 'Provider is required'],
      default: 'email'
    },
    googleId: {
      type: String,
      sparse: true, // Allows multiple null values but unique non-null values
      validate: {
        validator: function(this: IUser, v: string) {
          // Google ID is required only for Google provider
          if (this.provider === 'google') {
            return !!v;
          }
          return true;
        },
        message: 'Google ID is required for Google provider'
      }
    }
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Indexes for better query performance
userSchema.index({ email: 1 });
userSchema.index({ googleId: 1 });
userSchema.index({ provider: 1 });

// Pre-save middleware to generate avatar URL if not provided
userSchema.pre('save', function(next) {
  if (!this.avatarUrl && this.email) {
    // Generate a default avatar using a service like Gravatar or UI Avatars
    const emailHash = Buffer.from(this.email).toString('base64');
    this.avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(this.name)}&background=3b82f6&color=fff&size=200`;
  }
  next();
});

const User = mongoose.model<IUser>('User', userSchema);

export default User;
