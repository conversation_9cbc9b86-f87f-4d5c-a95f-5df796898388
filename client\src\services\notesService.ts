import { apiUtils } from '../config/api';
import { 
  Note, 
  NotesResponse, 
  NoteFormData, 
  SearchFilters, 
  NoteStats, 
  ApiResponse 
} from '../types';

class NotesService {
  // Get all notes with pagination and search
  async getNotes(filters: SearchFilters = {}): Promise<ApiResponse<NotesResponse>> {
    try {
      const params = new URLSearchParams();
      
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.query) params.append('search', filters.query);
      
      const queryString = params.toString();
      const url = queryString ? `/notes?${queryString}` : '/notes';
      
      return await apiUtils.get<NotesResponse>(url);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Get a single note by ID
  async getNote(id: string): Promise<ApiResponse<{ note: Note }>> {
    try {
      return await apiUtils.get<{ note: Note }>(`/notes/${id}`);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Create a new note
  async createNote(data: NoteFormData): Promise<ApiResponse<{ note: Note }>> {
    try {
      return await apiUtils.post<{ note: Note }>('/notes', data);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Update an existing note
  async updateNote(id: string, data: Partial<NoteFormData>): Promise<ApiResponse<{ note: Note }>> {
    try {
      return await apiUtils.put<{ note: Note }>(`/notes/${id}`, data);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Delete a note
  async deleteNote(id: string): Promise<ApiResponse> {
    try {
      return await apiUtils.delete(`/notes/${id}`);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Bulk delete notes
  async bulkDeleteNotes(noteIds: string[]): Promise<ApiResponse> {
    try {
      return await apiUtils.delete('/notes', {
        data: { noteIds }
      });
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Search notes
  async searchNotes(query: string, page: number = 1, limit: number = 10): Promise<ApiResponse<NotesResponse>> {
    try {
      const params = new URLSearchParams({
        q: query,
        page: page.toString(),
        limit: limit.toString()
      });
      
      return await apiUtils.get<NotesResponse>(`/notes/search?${params.toString()}`);
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Get note statistics
  async getNoteStats(): Promise<ApiResponse<{ stats: NoteStats }>> {
    try {
      return await apiUtils.get<{ stats: NoteStats }>('/notes/stats');
    } catch (error) {
      throw new Error(apiUtils.handleError(error));
    }
  }

  // Validation methods
  validateTitle(title: string): string | null {
    if (!title.trim()) return 'Title is required';
    if (title.trim().length > 200) return 'Title cannot exceed 200 characters';
    return null;
  }

  validateContent(content: string): string | null {
    if (!content.trim()) return 'Content is required';
    if (content.trim().length > 10000) return 'Content cannot exceed 10,000 characters';
    return null;
  }

  validateNoteForm(data: NoteFormData): { [key: string]: string } {
    const errors: { [key: string]: string } = {};
    
    const titleError = this.validateTitle(data.title);
    if (titleError) errors.title = titleError;
    
    const contentError = this.validateContent(data.content);
    if (contentError) errors.content = contentError;
    
    return errors;
  }

  // Utility methods
  formatNoteDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  }

  getWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  getReadingTime(content: string): number {
    const wordCount = this.getWordCount(content);
    const wordsPerMinute = 200; // Average reading speed
    return Math.ceil(wordCount / wordsPerMinute);
  }

  truncateContent(content: string, maxLength: number = 150): string {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength).trim() + '...';
  }

  highlightSearchTerm(text: string, searchTerm: string): string {
    if (!searchTerm.trim()) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  // Local storage methods for offline support
  getCachedNotes(): Note[] {
    try {
      const cached = localStorage.getItem('cachedNotes');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error reading cached notes:', error);
      return [];
    }
  }

  setCachedNotes(notes: Note[]): void {
    try {
      localStorage.setItem('cachedNotes', JSON.stringify(notes));
    } catch (error) {
      console.error('Error caching notes:', error);
    }
  }

  clearCachedNotes(): void {
    localStorage.removeItem('cachedNotes');
  }

  // Export notes as JSON
  exportNotesAsJson(notes: Note[]): void {
    const dataStr = JSON.stringify(notes, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `notes-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  // Export notes as text
  exportNotesAsText(notes: Note[]): void {
    const textContent = notes.map(note => 
      `Title: ${note.title}\nCreated: ${new Date(note.createdAt).toLocaleString()}\n\n${note.content}\n\n${'='.repeat(50)}\n\n`
    ).join('');
    
    const dataBlob = new Blob([textContent], { type: 'text/plain' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `notes-export-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}

export default new NotesService();
