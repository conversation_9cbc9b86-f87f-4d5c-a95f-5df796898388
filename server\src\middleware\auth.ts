import { Request, Response, NextFunction } from 'express';
import jwtUtils, { TokenPayload } from '../utils/jwt';
import { User } from '../models';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        email: string;
      };
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    userId: string;
    email: string;
  };
}

// Middleware to authenticate user using JWT
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | null = null;

    // Try to get token from cookies first (preferred method)
    if (req.cookies?.accessToken) {
      token = req.cookies.accessToken;
    }
    // Fallback to Authorization header
    else if (req.headers.authorization) {
      token = jwtUtils.extractTokenFromHeader(req.headers.authorization);
    }

    if (!token) {
      res.status(401).json({
        success: false,
        message: 'Access token is required'
      });
      return;
    }

    // Verify the token
    const payload: TokenPayload = jwtUtils.verifyAccessToken(token);

    // Check if user still exists
    const user = await User.findById(payload.userId).select('email');
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Attach user info to request
    req.user = {
      userId: payload.userId,
      email: payload.email
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error instanceof Error && error.message.includes('expired')) {
      res.status(401).json({
        success: false,
        message: 'Access token has expired',
        code: 'TOKEN_EXPIRED'
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Invalid access token'
      });
    }
  }
};

// Middleware to optionally authenticate user (doesn't fail if no token)
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    let token: string | null = null;

    // Try to get token from cookies first
    if (req.cookies?.accessToken) {
      token = req.cookies.accessToken;
    }
    // Fallback to Authorization header
    else if (req.headers.authorization) {
      token = jwtUtils.extractTokenFromHeader(req.headers.authorization);
    }

    if (token) {
      try {
        const payload: TokenPayload = jwtUtils.verifyAccessToken(token);
        
        // Check if user still exists
        const user = await User.findById(payload.userId).select('email');
        if (user) {
          req.user = {
            userId: payload.userId,
            email: payload.email
          };
        }
      } catch (error) {
        // Ignore token errors in optional auth
        console.log('Optional auth token error:', error);
      }
    }

    next();
  } catch (error) {
    // Don't fail the request for optional auth
    console.error('Optional authentication error:', error);
    next();
  }
};

// Middleware to refresh access token using refresh token
export const refreshTokenMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const refreshToken = req.cookies?.refreshToken;

    if (!refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Refresh token is required'
      });
      return;
    }

    // Verify refresh token
    const payload = jwtUtils.verifyRefreshToken(refreshToken);

    // Check if user still exists
    const user = await User.findById(payload.userId).select('email');
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Generate new access token
    const newAccessToken = jwtUtils.generateAccessToken({
      userId: user._id.toString(),
      email: user.email
    });

    // Set new access token cookie
    const isProduction = process.env.NODE_ENV === 'production';
    res.cookie('accessToken', newAccessToken, {
      httpOnly: true,
      secure: isProduction,
      sameSite: isProduction ? 'strict' : 'lax',
      maxAge: 15 * 60 * 1000, // 15 minutes
      path: '/'
    });

    // Attach user info to request
    req.user = {
      userId: user._id.toString(),
      email: user.email
    };

    next();
  } catch (error) {
    console.error('Refresh token error:', error);
    
    // Clear invalid refresh token
    res.clearCookie('refreshToken');
    res.clearCookie('accessToken');
    
    res.status(401).json({
      success: false,
      message: 'Invalid refresh token',
      code: 'REFRESH_TOKEN_INVALID'
    });
  }
};

// Middleware to check if user is authenticated and handle token refresh
export const requireAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // First try normal authentication
    await authenticateToken(req, res, () => {
      // If authentication successful, continue
      next();
    });
  } catch (error) {
    // If access token is expired, try to refresh
    if (error instanceof Error && error.message.includes('expired')) {
      try {
        await refreshTokenMiddleware(req, res, next);
      } catch (refreshError) {
        // If refresh also fails, return unauthorized
        res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }
    } else {
      // For other auth errors, return unauthorized
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
  }
};
