// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  provider: 'email' | 'google';
  createdAt: string;
}

// Note types
export interface Note {
  _id: string;
  userId: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    field: string;
    message: string;
    code?: string;
  }>;
}

// Pagination types
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface NotesResponse {
  notes: Note[];
  pagination: Pagination;
}

// Auth types
export interface LoginCredentials {
  email: string;
  otp: string;
  name?: string;
}

export interface RequestOtpData {
  email: string;
  name?: string;
}

export interface AuthResponse {
  user: User;
  isNewUser?: boolean;
}

// Form types
export interface SignUpFormData {
  email: string;
  name: string;
}

export interface SignInFormData {
  email: string;
}

export interface OtpFormData {
  otp: string;
}

export interface NoteFormData {
  title: string;
  content: string;
}

// Theme types
export type Theme = 'light' | 'dark';

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  page?: number;
  limit?: number;
}

// Note statistics
export interface NoteStats {
  totalNotes: number;
  totalWords: number;
  avgWordsPerNote: number;
  lastUpdated: string | null;
}
