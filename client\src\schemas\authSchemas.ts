import { z } from 'zod';

// Sign up form schema
export const signUpSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long'),
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name cannot exceed 50 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces')
});

// Sign in form schema
export const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long')
});

// OTP verification schema
export const otpSchema = z.object({
  otp: z
    .string()
    .min(1, 'Verification code is required')
    .regex(/^\d{6}$/, 'Verification code must be 6 digits')
    .length(6, 'Verification code must be exactly 6 digits')
});

// Combined schemas for multi-step forms
export const signUpWithOtpSchema = signUpSchema.merge(otpSchema);
export const signInWithOtpSchema = signInSchema.merge(otpSchema);

// Type exports
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type SignInFormData = z.infer<typeof signInSchema>;
export type OtpFormData = z.infer<typeof otpSchema>;
export type SignUpWithOtpData = z.infer<typeof signUpWithOtpSchema>;
export type SignInWithOtpData = z.infer<typeof signInWithOtpSchema>;
