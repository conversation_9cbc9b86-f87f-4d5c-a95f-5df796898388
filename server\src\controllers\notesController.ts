import { Request, Response } from 'express';
import { Note } from '../models';
import { AuthenticatedRequest } from '../middleware/auth';

class NotesController {
  // Get all notes for the authenticated user
  async getNotes(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { page = 1, limit = 10, search } = req.query;

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Get notes with pagination and search
      const result = await (Note as any).getUserNotes(
        userId,
        pageNum,
        limitNum,
        search as string
      );

      res.status(200).json({
        success: true,
        message: 'Notes retrieved successfully',
        data: result
      });

    } catch (error) {
      console.error('Get notes error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notes'
      });
    }
  }

  // Get a single note by ID
  async getNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      const note = await Note.findOne({ _id: id, userId });

      if (!note) {
        res.status(404).json({
          success: false,
          message: 'Note not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Note retrieved successfully',
        data: { note }
      });

    } catch (error) {
      console.error('Get note error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve note'
      });
    }
  }

  // Create a new note
  async createNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { title, content } = req.body;

      const note = new Note({
        userId,
        title,
        content
      });

      await note.save();

      res.status(201).json({
        success: true,
        message: 'Note created successfully',
        data: { note }
      });

    } catch (error) {
      console.error('Create note error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create note'
      });
    }
  }

  // Update an existing note
  async updateNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { id } = req.params;
      const { title, content } = req.body;

      // Build update object with only provided fields
      const updateData: any = {};
      if (title !== undefined) updateData.title = title;
      if (content !== undefined) updateData.content = content;
      updateData.updatedAt = new Date();

      const note = await Note.findOneAndUpdate(
        { _id: id, userId },
        updateData,
        { new: true, runValidators: true }
      );

      if (!note) {
        res.status(404).json({
          success: false,
          message: 'Note not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Note updated successfully',
        data: { note }
      });

    } catch (error) {
      console.error('Update note error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update note'
      });
    }
  }

  // Delete a note
  async deleteNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      const note = await Note.findOneAndDelete({ _id: id, userId });

      if (!note) {
        res.status(404).json({
          success: false,
          message: 'Note not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Note deleted successfully',
        data: { deletedNote: { id: note._id, title: note.title } }
      });

    } catch (error) {
      console.error('Delete note error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete note'
      });
    }
  }

  // Get user's note statistics
  async getNoteStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;

      const stats = await (Note as any).getUserStats(userId);

      res.status(200).json({
        success: true,
        message: 'Note statistics retrieved successfully',
        data: { stats }
      });

    } catch (error) {
      console.error('Get note stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve note statistics'
      });
    }
  }

  // Search notes
  async searchNotes(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { q: query, page = 1, limit = 10 } = req.query;

      if (!query || typeof query !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
        return;
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Use the getUserNotes method with search
      const result = await (Note as any).getUserNotes(
        userId,
        pageNum,
        limitNum,
        query
      );

      res.status(200).json({
        success: true,
        message: 'Search completed successfully',
        data: {
          ...result,
          searchQuery: query
        }
      });

    } catch (error) {
      console.error('Search notes error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search notes'
      });
    }
  }

  // Bulk delete notes
  async bulkDeleteNotes(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.userId;
      const { noteIds } = req.body;

      if (!Array.isArray(noteIds) || noteIds.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Note IDs array is required'
        });
        return;
      }

      // Validate all IDs are valid MongoDB ObjectIds
      const validIds = noteIds.filter(id => /^[0-9a-fA-F]{24}$/.test(id));

      if (validIds.length !== noteIds.length) {
        res.status(400).json({
          success: false,
          message: 'Some note IDs are invalid'
        });
        return;
      }

      const result = await Note.deleteMany({
        _id: { $in: validIds },
        userId
      });

      res.status(200).json({
        success: true,
        message: `${result.deletedCount} notes deleted successfully`,
        data: {
          deletedCount: result.deletedCount,
          requestedCount: noteIds.length
        }
      });

    } catch (error) {
      console.error('Bulk delete notes error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete notes'
      });
    }
  }
}

export default new NotesController();
