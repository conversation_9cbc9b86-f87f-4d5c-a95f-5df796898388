import { Request, Response } from 'express';
import { User, Otp } from '../models';
import jwtUtils from '../utils/jwt';
import emailService from '../services/emailService';

class AuthController {
  // Request OTP for email verification
  async requestOtp(req: Request, res: Response): Promise<void> {
    try {
      const { email, name } = req.body;

      // Generate 6-digit OTP
      const otpCode = Math.floor(100000 + Math.random() * 900000).toString();

      // Create OTP record (this will remove any existing OTP for this email)
      await Otp.createOtp(email, otpCode);

      // Send OTP email
      const emailSent = await emailService.sendOTPEmail(email, otpCode, name);

      if (!emailSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to send OTP email. Please try again.'
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'OTP sent successfully to your email',
        data: {
          email,
          expiresIn: 600 // 10 minutes in seconds
        }
      });

    } catch (error) {
      console.error('Request OTP error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send OTP. Please try again.'
      });
    }
  }

  // Verify OTP and login/signup user
  async verifyOtp(req: Request, res: Response): Promise<void> {
    try {
      const { email, otp, name } = req.body;

      // Find OTP record
      const otpRecord = await Otp.findOne({ email });

      if (!otpRecord) {
        res.status(400).json({
          success: false,
          message: 'OTP not found or expired. Please request a new one.'
        });
        return;
      }

      // Check if OTP is expired
      if (otpRecord.isExpired()) {
        await Otp.deleteOne({ email });
        res.status(400).json({
          success: false,
          message: 'OTP has expired. Please request a new one.'
        });
        return;
      }

      // Check attempts limit
      if (otpRecord.attempts >= 5) {
        await Otp.deleteOne({ email });
        res.status(400).json({
          success: false,
          message: 'Too many failed attempts. Please request a new OTP.'
        });
        return;
      }

      // Verify OTP
      const isValidOtp = await otpRecord.verifyCode(otp);

      if (!isValidOtp) {
        res.status(400).json({
          success: false,
          message: 'Invalid OTP. Please try again.',
          attemptsRemaining: 5 - otpRecord.attempts
        });
        return;
      }

      // OTP is valid, find or create user
      let user = await User.findOne({ email });

      if (!user) {
        // Create new user (signup)
        if (!name) {
          res.status(400).json({
            success: false,
            message: 'Name is required for new users'
          });
          return;
        }

        user = new User({
          email,
          name,
          provider: 'email'
        });

        await user.save();

        // Send welcome email
        await emailService.sendWelcomeEmail(email, name);
      }

      // Generate tokens
      const accessToken = jwtUtils.generateAccessToken({
        userId: user._id.toString(),
        email: user.email
      });

      const refreshToken = jwtUtils.generateRefreshToken({
        userId: user._id.toString(),
        tokenVersion: 1
      });

      // Set tokens as HTTP-only cookies
      jwtUtils.setTokenCookies(res, accessToken, refreshToken);

      // Clean up OTP
      await Otp.deleteOne({ email });

      res.status(200).json({
        success: true,
        message: user.createdAt.getTime() === user.updatedAt.getTime() ? 
          'Account created successfully' : 'Login successful',
        data: {
          user: {
            id: user._id,
            email: user.email,
            name: user.name,
            avatarUrl: user.avatarUrl,
            provider: user.provider
          },
          isNewUser: user.createdAt.getTime() === user.updatedAt.getTime()
        }
      });

    } catch (error) {
      console.error('Verify OTP error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to verify OTP. Please try again.'
      });
    }
  }

  // Google OAuth callback
  async googleCallback(req: Request, res: Response): Promise<void> {
    try {
      const user = req.user as any;

      if (!user) {
        res.redirect(`${process.env.CLIENT_URL}/auth/error?message=Authentication failed`);
        return;
      }

      // Generate tokens
      const accessToken = jwtUtils.generateAccessToken({
        userId: user._id.toString(),
        email: user.email
      });

      const refreshToken = jwtUtils.generateRefreshToken({
        userId: user._id.toString(),
        tokenVersion: 1
      });

      // Set tokens as HTTP-only cookies
      jwtUtils.setTokenCookies(res, accessToken, refreshToken);

      // Redirect to dashboard
      res.redirect(`${process.env.CLIENT_URL}/dashboard`);

    } catch (error) {
      console.error('Google callback error:', error);
      res.redirect(`${process.env.CLIENT_URL}/auth/error?message=Authentication failed`);
    }
  }

  // Refresh access token
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const refreshToken = req.cookies?.refreshToken;

      if (!refreshToken) {
        res.status(401).json({
          success: false,
          message: 'Refresh token is required'
        });
        return;
      }

      // Verify refresh token
      const payload = jwtUtils.verifyRefreshToken(refreshToken);

      // Check if user still exists
      const user = await User.findById(payload.userId);
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Generate new access token
      const newAccessToken = jwtUtils.generateAccessToken({
        userId: user._id.toString(),
        email: user.email
      });

      // Set new access token cookie
      const isProduction = process.env.NODE_ENV === 'production';
      res.cookie('accessToken', newAccessToken, {
        httpOnly: true,
        secure: isProduction,
        sameSite: isProduction ? 'strict' : 'lax',
        maxAge: 15 * 60 * 1000, // 15 minutes
        path: '/'
      });

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          user: {
            id: user._id,
            email: user.email,
            name: user.name,
            avatarUrl: user.avatarUrl,
            provider: user.provider
          }
        }
      });

    } catch (error) {
      console.error('Refresh token error:', error);
      
      // Clear invalid cookies
      jwtUtils.clearTokenCookies(res);
      
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }
  }

  // Logout user
  async logout(req: Request, res: Response): Promise<void> {
    try {
      // Clear token cookies
      jwtUtils.clearTokenCookies(res);

      res.status(200).json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to logout'
      });
    }
  }

  // Get current user info
  async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const user = await User.findById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: user._id,
            email: user.email,
            name: user.name,
            avatarUrl: user.avatarUrl,
            provider: user.provider,
            createdAt: user.createdAt
          }
        }
      });

    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user information'
      });
    }
  }
}

export default new AuthController();
