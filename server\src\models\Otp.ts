import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IOtp extends Document {
  email: string;
  codeHash: string;
  expiresAt: Date;
  attempts: number;
  createdAt: Date;
  isExpired(): boolean;
  verifyCode(code: string): Promise<boolean>;
  incrementAttempts(): Promise<void>;
}

const otpSchema = new Schema<IOtp>(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      lowercase: true,
      trim: true,
      match: [
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        'Please provide a valid email address'
      ]
    },
    codeHash: {
      type: String,
      required: [true, 'OTP code hash is required']
    },
    expiresAt: {
      type: Date,
      required: [true, 'Expiration time is required'],
      default: () => new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
    },
    attempts: {
      type: Number,
      default: 0,
      max: [5, 'Maximum attempts exceeded']
    }
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.codeHash;
        delete ret.__v;
        return ret;
      }
    }
  }
);

// Indexes for better query performance and automatic cleanup
otpSchema.index({ email: 1 });
otpSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for automatic cleanup

// Instance method to check if OTP is expired
otpSchema.methods.isExpired = function(): boolean {
  return new Date() > this.expiresAt;
};

// Instance method to verify OTP code
otpSchema.methods.verifyCode = async function(code: string): Promise<boolean> {
  if (this.isExpired()) {
    return false;
  }
  
  if (this.attempts >= 5) {
    return false;
  }

  const isValid = await bcrypt.compare(code, this.codeHash);
  
  if (!isValid) {
    await this.incrementAttempts();
  }
  
  return isValid;
};

// Instance method to increment attempts
otpSchema.methods.incrementAttempts = async function(): Promise<void> {
  this.attempts += 1;
  await this.save();
};

// Static method to create new OTP
otpSchema.statics.createOtp = async function(email: string, code: string): Promise<IOtp> {
  // Remove any existing OTP for this email
  await this.deleteMany({ email });
  
  // Hash the OTP code
  const saltRounds = 12;
  const codeHash = await bcrypt.hash(code, saltRounds);
  
  // Create new OTP
  const otp = new this({
    email,
    codeHash,
    expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
  });
  
  return await otp.save();
};

// Static method to generate random OTP code
otpSchema.statics.generateCode = function(): string {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
};

// Pre-save middleware to ensure only one active OTP per email
otpSchema.pre('save', async function(next) {
  if (this.isNew) {
    // Remove any existing OTP for this email
    await mongoose.model('Otp').deleteMany({ 
      email: this.email, 
      _id: { $ne: this._id } 
    });
  }
  next();
});

const Otp = mongoose.model<IOtp>('Otp', otpSchema);

export default Otp;
