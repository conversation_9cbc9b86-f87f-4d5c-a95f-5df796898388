import express from 'express';
import notesController from '../controllers/notesController';
import { authenticateToken } from '../middleware/auth';
import { validate, sanitizeInput, validateObjectId } from '../middleware/validation';
import {
  createNoteSchema,
  updateNoteSchema,
  deleteNoteSchema,
  getNotesSchema,
  getNoteSchema
} from '../validators/notes';

const router = express.Router();

// Apply authentication to all note routes
router.use(authenticateToken);

// Get all notes for user (with pagination and search)
router.get(
  '/',
  sanitizeInput,
  validate(getNotesSchema),
  notesController.getNotes
);

// Search notes
router.get(
  '/search',
  sanitizeInput,
  notesController.searchNotes
);

// Get user's note statistics
router.get(
  '/stats',
  notesController.getNoteStats
);

// Get single note by ID
router.get(
  '/:id',
  validateObjectId('id'),
  validate(getNoteSchema),
  notesController.getNote
);

// Create new note
router.post(
  '/',
  sanitizeInput,
  validate(createNoteSchema),
  notesController.createNote
);

// Update note
router.put(
  '/:id',
  validateObjectId('id'),
  sanitizeInput,
  validate(updateNoteSchema),
  notesController.updateNote
);

// Partial update note (PATCH)
router.patch(
  '/:id',
  validateObjectId('id'),
  sanitizeInput,
  validate(updateNoteSchema),
  notesController.updateNote
);

// Delete note
router.delete(
  '/:id',
  validateObjectId('id'),
  validate(deleteNoteSchema),
  notesController.deleteNote
);

// Bulk delete notes
router.delete(
  '/',
  sanitizeInput,
  notesController.bulkDeleteNotes
);

// Health check for notes service
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Notes service is healthy',
    timestamp: new Date().toISOString()
  });
});

export default router;
