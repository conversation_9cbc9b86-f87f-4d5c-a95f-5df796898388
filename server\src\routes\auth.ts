import express from 'express';
import passport from '../config/passport';
import authController from '../controllers/authController';
import { validate, validateRateLimit, sanitizeInput } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { requestOtpSchema, verifyOtpSchema } from '../validators/auth';

const router = express.Router();

// Apply rate limiting to sensitive auth endpoints
const otpRateLimit = validateRateLimit(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
const authRateLimit = validateRateLimit(10, 15 * 60 * 1000); // 10 attempts per 15 minutes

// Request OTP endpoint
router.post(
  '/request-otp',
  otpRateLimit,
  sanitizeInput,
  validate(requestOtpSchema),
  authController.requestOtp
);

// Verify OTP endpoint
router.post(
  '/verify-otp',
  authRateLimit,
  sanitizeInput,
  validate(verifyOtpSchema),
  authController.verifyOtp
);

// Google OAuth routes
router.get(
  '/google',
  passport.authenticate('google', {
    scope: ['profile', 'email']
  })
);

router.get(
  '/google/callback',
  passport.authenticate('google', {
    failureRedirect: `${process.env.CLIENT_URL}/auth/error`,
    session: false
  }),
  authController.googleCallback
);

// Refresh token endpoint
router.post(
  '/refresh',
  authController.refreshToken
);

// Logout endpoint
router.post(
  '/logout',
  authController.logout
);

// Get current user endpoint (protected)
router.get(
  '/me',
  authenticateToken,
  authController.getCurrentUser
);

// Health check for auth service
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Auth service is healthy',
    timestamp: new Date().toISOString()
  });
});

export default router;
