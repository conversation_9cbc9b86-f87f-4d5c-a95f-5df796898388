# 📝 Note Taking App

A modern, full-stack note-taking application built with React, TypeScript, Node.js, and MongoDB. Features secure authentication, real-time updates, and a beautiful dark/light mode interface.

## ✨ Features

### 🔐 Authentication
- **Email + OTP Verification**: Secure passwordless authentication
- **Google OAuth**: Quick sign-in with Google account
- **JWT Tokens**: Secure session management with HTTP-only cookies
- **Auto-refresh**: Seamless token renewal

### 📱 User Interface
- **Modern Design**: Clean, intuitive interface with Tailwind CSS
- **Dark/Light Mode**: Automatic theme detection with manual toggle
- **Responsive**: Works perfectly on desktop, tablet, and mobile
- **Animations**: Smooth transitions with Framer Motion
- **Accessibility**: WCAG compliant with keyboard navigation

### 📝 Note Management
- **Create & Edit**: Rich text editing with auto-save
- **Search**: Fast full-text search across all notes
- **Grid/List View**: Toggle between different viewing modes
- **Statistics**: Word count, reading time, and usage stats
- **Export**: Download notes as JSON or text files

### 🚀 Performance
- **Lazy Loading**: Code splitting for faster initial load
- **Caching**: Smart caching strategies for offline support
- **Error Handling**: Comprehensive error boundaries
- **Loading States**: Beautiful loading animations

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Hook Form** + **Zod** for form validation
- **Axios** for API calls
- **React Router** for navigation

### Backend
- **Node.js** with **Express**
- **TypeScript** for type safety
- **MongoDB** with **Mongoose**
- **JWT** for authentication
- **Nodemailer** for email services
- **Passport.js** for OAuth

### Security
- **Helmet.js** for security headers
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **CORS** configuration
- **HTTP-only cookies** for tokens

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- MongoDB (local or cloud)
- Gmail account for email service

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd note-taking-app
```

2. **Install dependencies**
```bash
# Install root dependencies
npm run install:all

# Or install manually
npm install
cd server && npm install
cd ../client && npm install
```

3. **Environment Setup**
```bash
# Copy environment template
cp server/.env.example server/.env
```

4. **Configure Environment Variables**
Edit `server/.env` with your settings:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/note-taking-app

# JWT Secrets (generate strong random strings)
JWT_ACCESS_SECRET=your-super-secret-access-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here

# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration (Gmail)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password

# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000
SESSION_SECRET=your-session-secret-key
```

5. **Start Development Servers**
```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:server  # Backend on http://localhost:5000
npm run dev:client  # Frontend on http://localhost:3000
```

## 📁 Project Structure

```
note-taking-app/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── contexts/       # React contexts (Auth, Theme)
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── types/          # TypeScript type definitions
│   │   ├── schemas/        # Zod validation schemas
│   │   └── config/         # Configuration files
│   ├── package.json
│   └── tailwind.config.js
├── server/                 # Node.js backend
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   ├── config/         # Configuration files
│   │   └── validators/     # Request validators
│   ├── package.json
│   └── tsconfig.json
├── package.json            # Root package.json
└── README.md
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start both frontend and backend
npm run dev:server       # Start backend only
npm run dev:client       # Start frontend only

# Building
npm run build           # Build both frontend and backend
npm run build:server    # Build backend only
npm run build:client    # Build frontend only

# Production
npm start              # Start production server

# Testing
npm test               # Run tests
```

### API Endpoints

#### Authentication
- `POST /api/auth/request-otp` - Request OTP for email
- `POST /api/auth/verify-otp` - Verify OTP and login
- `GET /api/auth/google` - Google OAuth login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user

#### Notes
- `GET /api/notes` - Get user's notes (with pagination)
- `GET /api/notes/:id` - Get specific note
- `POST /api/notes` - Create new note
- `PUT /api/notes/:id` - Update note
- `DELETE /api/notes/:id` - Delete note
- `GET /api/notes/search` - Search notes
- `GET /api/notes/stats` - Get user statistics

## 🔒 Security Features

- **Rate Limiting**: Prevents brute force attacks
- **Input Validation**: All inputs validated with Zod
- **XSS Protection**: Content sanitization
- **CSRF Protection**: Secure cookie configuration
- **SQL Injection**: MongoDB prevents SQL injection
- **Secure Headers**: Helmet.js security headers

## 🌐 Deployment

### Environment Variables for Production
```env
NODE_ENV=production
CLIENT_URL=https://your-domain.com
MONGODB_URI=mongodb+srv://user:<EMAIL>/notes
# ... other production values
```

### Build for Production
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [React](https://reactjs.org/) - Frontend framework
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [Express.js](https://expressjs.com/) - Backend framework
- [MongoDB](https://www.mongodb.com/) - Database
- [Lucide Icons](https://lucide.dev/) - Icon library

## 📞 Support

If you have any questions or need help, please open an issue or contact the development team.

---

Made with ❤️ by the Note Taking App Team
