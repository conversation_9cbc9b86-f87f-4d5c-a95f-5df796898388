import nodemailer from 'nodemailer';
import { google } from 'googleapis';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.initializeTransporter();
  }

  private async initializeTransporter() {
    const mailHost = process.env.MAIL_HOST;
    const mailPort = parseInt(process.env.MAIL_PORT || '587');
    const mailUser = process.env.MAIL_USER;
    const mailPass = process.env.MAIL_PASS;

    if (!mailHost || !mailUser || !mailPass) {
      throw new Error('Email configuration is incomplete. Please check environment variables.');
    }

    this.transporter = nodemailer.createTransporter({
      host: mailHost,
      port: mailPort,
      secure: mailPort === 465, // true for 465, false for other ports
      auth: {
        user: mailUser,
        pass: mailPass
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify connection configuration
    try {
      await this.transporter.verify();
      console.log('Email service is ready to send messages');
    } catch (error) {
      console.error('Email service configuration error:', error);
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions = {
        from: {
          name: 'Note Taking App',
          address: process.env.MAIL_USER!
        },
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || this.stripHtml(options.html)
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', info.messageId);
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  async sendOTPEmail(email: string, otp: string, name?: string): Promise<boolean> {
    const subject = 'Your OTP Code - Note Taking App';
    const html = this.generateOTPEmailTemplate(otp, name);
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    });
  }

  async sendWelcomeEmail(email: string, name: string): Promise<boolean> {
    const subject = 'Welcome to Note Taking App!';
    const html = this.generateWelcomeEmailTemplate(name);
    
    return await this.sendEmail({
      to: email,
      subject,
      html
    });
  }

  private generateOTPEmailTemplate(otp: string, name?: string): string {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your OTP Code</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); padding: 40px 20px; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 28px; font-weight: 600; }
            .content { padding: 40px 20px; text-align: center; }
            .otp-code { background-color: #f8fafc; border: 2px dashed #3b82f6; border-radius: 12px; padding: 30px; margin: 30px 0; }
            .otp-number { font-size: 36px; font-weight: bold; color: #1d4ed8; letter-spacing: 8px; margin: 10px 0; }
            .footer { background-color: #f8fafc; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
            .warning { background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; text-align: left; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Verification Code</h1>
            </div>
            <div class="content">
                ${name ? `<h2>Hello ${name}!</h2>` : '<h2>Hello!</h2>'}
                <p>You requested a verification code for your Note Taking App account. Use the code below to complete your authentication:</p>
                
                <div class="otp-code">
                    <p style="margin: 0; color: #374151; font-weight: 500;">Your verification code is:</p>
                    <div class="otp-number">${otp}</div>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">This code will expire in 10 minutes</p>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Security Notice:</strong><br>
                    • Never share this code with anyone<br>
                    • We will never ask for this code via phone or email<br>
                    • If you didn't request this code, please ignore this email
                </div>
            </div>
            <div class="footer">
                <p>This is an automated message from Note Taking App. Please do not reply to this email.</p>
                <p>&copy; 2024 Note Taking App. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  private generateWelcomeEmailTemplate(name: string): string {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Note Taking App</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); padding: 40px 20px; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 28px; font-weight: 600; }
            .content { padding: 40px 20px; }
            .feature { display: flex; align-items: center; margin: 20px 0; }
            .feature-icon { font-size: 24px; margin-right: 15px; }
            .cta-button { background-color: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 20px 0; font-weight: 600; }
            .footer { background-color: #f8fafc; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Welcome to Note Taking App!</h1>
            </div>
            <div class="content">
                <h2>Hello ${name}!</h2>
                <p>Welcome to your new digital notebook! We're excited to have you on board.</p>
                
                <h3>What you can do with Note Taking App:</h3>
                <div class="feature">
                    <span class="feature-icon">📝</span>
                    <span>Create and organize your notes effortlessly</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">🔍</span>
                    <span>Search through your notes instantly</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">🌙</span>
                    <span>Enjoy dark mode for comfortable writing</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">📱</span>
                    <span>Access your notes from any device</span>
                </div>
                
                <p>Ready to start taking notes? Click the button below to get started:</p>
                <a href="${process.env.CLIENT_URL}" class="cta-button">Start Taking Notes</a>
                
                <p>If you have any questions or need help, feel free to reach out to our support team.</p>
                
                <p>Happy note-taking!<br>The Note Taking App Team</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Note Taking App. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

export default new EmailService();
