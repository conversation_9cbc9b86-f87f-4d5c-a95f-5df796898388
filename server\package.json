{"name": "note-taking-server", "version": "1.0.0", "description": "Backend server for note-taking application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "nodemailer": "^6.9.7", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "cookie-parser": "^1.4.6", "dotenv": "^16.3.1", "zod": "^3.22.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "express-validator": "^7.0.1", "express-session": "^1.17.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.14", "@types/cors": "^2.8.17", "@types/cookie-parser": "^1.4.6", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/express-session": "^1.17.10", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}