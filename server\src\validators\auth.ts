import { z } from 'zod';

// Request OTP validation schema
export const requestOtpSchema = z.object({
  body: z.object({
    email: z
      .string()
      .email('Please provide a valid email address')
      .min(1, 'Email is required')
      .max(255, 'Email is too long')
      .toLowerCase()
      .trim(),
    name: z
      .string()
      .min(2, 'Name must be at least 2 characters long')
      .max(50, 'Name cannot exceed 50 characters')
      .trim()
      .optional()
  })
});

// Verify OTP validation schema
export const verifyOtpSchema = z.object({
  body: z.object({
    email: z
      .string()
      .email('Please provide a valid email address')
      .min(1, 'Email is required')
      .toLowerCase()
      .trim(),
    otp: z
      .string()
      .regex(/^\d{6}$/, 'OTP must be a 6-digit number')
      .min(6, 'OTP must be 6 digits')
      .max(6, 'OTP must be 6 digits'),
    name: z
      .string()
      .min(2, 'Name must be at least 2 characters long')
      .max(50, 'Name cannot exceed 50 characters')
      .trim()
      .optional()
  })
});

// Refresh token validation schema
export const refreshTokenSchema = z.object({
  cookies: z.object({
    refreshToken: z
      .string()
      .min(1, 'Refresh token is required')
  })
});

// Types for TypeScript
export type RequestOtpInput = z.infer<typeof requestOtpSchema>;
export type VerifyOtpInput = z.infer<typeof verifyOtpSchema>;
export type RefreshTokenInput = z.infer<typeof refreshTokenSchema>;
