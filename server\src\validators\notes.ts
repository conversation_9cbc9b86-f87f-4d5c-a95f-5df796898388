import { z } from 'zod';

// Create note validation schema
export const createNoteSchema = z.object({
  body: z.object({
    title: z
      .string()
      .min(1, 'Title is required')
      .max(200, 'Title cannot exceed 200 characters')
      .trim(),
    content: z
      .string()
      .min(1, 'Content is required')
      .max(10000, 'Content cannot exceed 10,000 characters')
      .trim()
  })
});

// Update note validation schema
export const updateNoteSchema = z.object({
  params: z.object({
    id: z
      .string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid note ID format')
  }),
  body: z.object({
    title: z
      .string()
      .min(1, 'Title is required')
      .max(200, 'Title cannot exceed 200 characters')
      .trim()
      .optional(),
    content: z
      .string()
      .min(1, 'Content is required')
      .max(10000, 'Content cannot exceed 10,000 characters')
      .trim()
      .optional()
  }).refine(
    (data) => data.title !== undefined || data.content !== undefined,
    {
      message: 'At least one field (title or content) must be provided',
      path: ['body']
    }
  )
});

// Delete note validation schema
export const deleteNoteSchema = z.object({
  params: z.object({
    id: z
      .string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid note ID format')
  })
});

// Get notes validation schema (for query parameters)
export const getNotesSchema = z.object({
  query: z.object({
    page: z
      .string()
      .regex(/^\d+$/, 'Page must be a positive number')
      .transform(Number)
      .refine((val) => val > 0, 'Page must be greater than 0')
      .optional()
      .default('1'),
    limit: z
      .string()
      .regex(/^\d+$/, 'Limit must be a positive number')
      .transform(Number)
      .refine((val) => val > 0 && val <= 100, 'Limit must be between 1 and 100')
      .optional()
      .default('10'),
    search: z
      .string()
      .max(100, 'Search query cannot exceed 100 characters')
      .trim()
      .optional()
  })
});

// Get single note validation schema
export const getNoteSchema = z.object({
  params: z.object({
    id: z
      .string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid note ID format')
  })
});

// Types for TypeScript
export type CreateNoteInput = z.infer<typeof createNoteSchema>;
export type UpdateNoteInput = z.infer<typeof updateNoteSchema>;
export type DeleteNoteInput = z.infer<typeof deleteNoteSchema>;
export type GetNotesInput = z.infer<typeof getNotesSchema>;
export type GetNoteInput = z.infer<typeof getNoteSchema>;
